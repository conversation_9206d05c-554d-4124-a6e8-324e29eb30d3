<mujoco model="go2 scene">
  <include file="go2.xml" />

  <statistic center="0 0 0.1" extent="0.8" />

  <visual>
    <headlight diffuse="0.6 0.6 0.6" ambient="0.3 0.3 0.3" specular="0 0 0" />
    <rgba haze="0.15 0.25 0.35 1" />
    <global azimuth="-130" elevation="-20" />
  </visual>

  <asset>
    <material name="step1" rgba="0.8 0.3 0.3 1" /> <!-- 红色 -->
    <material name="step2" rgba="0.3 0.8 0.3 1" /> <!-- 绿色 -->
    <material name="step3" rgba="0.3 0.3 0.8 1" /> <!-- 蓝色 -->
    <texture type="skybox" builtin="gradient" rgb1="0.3 0.5 0.7" rgb2="0 0 0" width="512" height="3072" />
    <texture type="2d" name="groundplane" builtin="checker" mark="edge" rgb1="0.2 0.3 0.4" rgb2="0.1 0.2 0.3" markrgb="0.8 0.8 0.8" width="300" height="300" />
    <material name="groundplane" texture="groundplane" texuniform="true" texrepeat="5 5" reflectance="0.2" />
    <hfield name="perlin_hfield" size="1.0 0.75 0.2 0.2" file="../height_field.png" />
    <hfield name="image_hfield" size="1.0 1.0 0.02 0.1" file="../unitree_hfield.png" />
  </asset>

  <worldbody>
    <light pos="0 0 1.5" dir="0 0 -1" directional="true" />
    <geom name="floor" size="0 0 0.05" type="plane" material="groundplane" />

    <!-- 第一层台阶 -->
    <geom pos="1.5 0.0 0.00" type="box" size="0.80 0.50 0.08" material="step1" />

    <!-- 第二层台阶 (比第一层高4cm) -->
    <geom pos="1.8 0.0 0.03" type="box" size="0.40 0.50 0.16" material="step2" />

    <!-- 第三层台阶 (与第一层相同高) -->
    <geom pos="1.95 0.0 0.00" type="box" size="0.80 0.50 0.08" material="step3" />
  </worldbody>
</mujoco>