<mujoco model="go2 scene with random boxes">
  <include file="go2.xml"/>

  <statistic center="0 0 0.1" extent="0.8"/>

  <visual>
    <headlight diffuse="0.6 0.6 0.6" ambient="0.3 0.3 0.3" specular="0 0 0"/>
    <rgba haze="0.15 0.25 0.35 1"/>
    <global azimuth="-130" elevation="-20"/>
  </visual>

  <asset>
    <texture type="skybox" builtin="gradient" rgb1="0.3 0.5 0.7" rgb2="0 0 0"
             width="512" height="3072"/>
    <texture type="2d" name="groundplane" builtin="checker" mark="edge"
             rgb1="0.2 0.3 0.4" rgb2="0.1 0.2 0.3" markrgb="0.8 0.8 0.8"
             width="300" height="300"/>
    <material name="groundplane" texture="groundplane" texuniform="true"
              texrepeat="5 5" reflectance="0.2"/></asset>
  <worldbody>
    <light pos="0 0 1.5" dir="0 0 -1" directional="true"/>
    <geom name="floor" size="0 0 0.05" type="plane" material="groundplane"/>

    <!-- 50 个随机青色盒子，高2 cm，长6 cm，宽3 cm -->
    <!-- ↓↓↓ 把 Python 生成的那 50 行粘到这里 ↓↓↓ -->
    <geom type="box" size="0.05 0.025 0.025" pos="-0.682 0.533 0.025" euler="0 0 293.8" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.207 -0.882 0.025" euler="0 0 55.4" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.431 -0.780 0.025" euler="0 0 162.0" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.892 -0.144 0.025" euler="0 0 123.7" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.943 0.285 0.025" euler="0 0 17.5" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.111 0.955 0.025" euler="0 0 259.3" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.294 -0.453 0.025" euler="0 0 312.6" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.745 0.598 0.025" euler="0 0 202.1" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.175 0.388 0.025" euler="0 0 88.9" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.614 -0.731 0.025" euler="0 0 174.4" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.057 0.211 0.025" euler="0 0 31.2" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.959 0.189 0.025" euler="0 0 276.5" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.553 -0.263 0.025" euler="0 0 145.8" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.418 0.812 0.025" euler="0 0 198.3" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.824 -0.061 0.025" euler="0 0 87.2" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.012 0.580 0.025" euler="0 0 329.7" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.674 -0.128 0.025" euler="0 0 254.1" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.314 0.717 0.025" euler="0 0 111.6" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.194 0.073 0.025" euler="0 0 48.9" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.469 -0.914 0.025" euler="0 0 186.4" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.855 0.447 0.025" euler="0 0 215.7" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.228 0.804 0.025" euler="0 0 126.3" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.387 -0.655 0.025" euler="0 0 32.8" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.761 0.041 0.025" euler="0 0 298.5" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.532 0.969 0.025" euler="0 0 73.9" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.109 -0.547 0.025" euler="0 0 160.2" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.996 -0.312 0.025" euler="0 0 241.7" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.637 0.526 0.025" euler="0 0 195.4" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.245 0.393 0.025" euler="0 0 108.6" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.482 -0.169 0.025" euler="0 0 36.8" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.661 0.184 0.025" euler="0 0 278.3" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.314 -0.725 0.025" euler="0 0 149.7" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.128 0.857 0.025" euler="0 0 224.1" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.975 0.272 0.025" euler="0 0 67.5" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.789 -0.093 0.025" euler="0 0 303.2" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.193 0.632 0.025" euler="0 0 158.9" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.542 0.711 0.025" euler="0 0 83.7" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.688 -0.408 0.025" euler="0 0 256.4" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.097 0.255 0.025" euler="0 0 121.5" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.425 0.378 0.025" euler="0 0 194.3" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.836 0.047 0.025" euler="0 0 48.7" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.812 0.693 0.025" euler="0 0 287.6" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.255 0.134 0.025" euler="0 0 175.8" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.089 -0.821 0.025" euler="0 0 92.4" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.967 0.512 0.025" euler="0 0 219.3" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.764 -0.187 0.025" euler="0 0 134.1" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.544 -0.603 0.025" euler="0 0 59.8" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.437 0.894 0.025" euler="0 0 242.7" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.183 0.977 0.025" euler="0 0 108.3" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.902 -0.554 0.025" euler="0 0 37.1" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.677 -0.228 0.025" euler="0 0 263.9" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="-0.256 -0.495 0.025" euler="0 0 190.4" rgba="0 1 1 1"/>
    <geom type="box" size="0.05 0.025 0.025" pos="0.312 0.670 0.025" euler="0 0 74.2" rgba="0 1 1 1"/>
    <!-- ... 其余 48 行 ... -->
  </worldbody>
</mujoco>