<mujoco model="go2 scene with payload">
    <include file="go2.xml"/>
    
    <statistic center="0 0 0.1" extent="0.8"/>
    
    <!-- 负载定义 -->
    <asset>
        <material name="payload_material" rgba="1 0 0 0.8"/>
        <texture type="skybox" builtin="gradient" rgb1="0.3 0.5 0.7" rgb2="0 0 0" width="512" height="3072"/>
        <texture type="2d" name="groundplane" builtin="checker" mark="edge" rgb1="0.2 0.3 0.4" rgb2="0.1 0.2 0.3"
        markrgb="0.8 0.8 0.8" width="300" height="300"/>
        <material name="groundplane" texture="groundplane" texuniform="true" texrepeat="5 5" reflectance="0.2"/>
    </asset>   

    <!-- 设置重力 -->
    <option gravity="0 0 -9.81"/>

    <worldbody>
        <!-- 地面和环境 -->
        <light pos="0 0 1.5" dir="0 0 -1" directional="true"/>
        <geom name="floor" size="0 0 0.05" type="plane" material="groundplane" friction="1.0 0.1 0.05"/>

        <!-- 在机器人背部添加负载 -->
        <body name="robot_payload" pos="0 0 0.6">
            <inertial pos="0 0 0" mass="3.0" diaginertia="0.02 0.02 0.01"/>
            <geom name="payload_box" type="box" size="0.15 0.1 0.08" material="payload_material" friction="0.8 0.05 0.1"/>
        </body>
    </worldbody>
    
    <!-- 使用 equality 约束将负载固定到机器人 base_link -->
    <equality>
        <weld body1="base_link" body2="robot_payload" relpose="0 0 0.15 1 0 0 0"/>
    </equality>
</mujoco>
